"""
Database module for ArLocal Python
Handles SQLite database operations for wallets, chunks, transactions, and blocks
"""

import sqlite3
import json
import time
import os
from typing import Dict, List, Optional, Any, Tuple
from contextlib import contextmanager
import logging

logger = logging.getLogger(__name__)


class ArLocalDatabase:
    """SQLite database manager for ArLocal"""
    
    def __init__(self, db_path: str = ':memory:', data_dir: str = 'data'):
        self.db_path = db_path
        self.data_dir = data_dir
        self.connection = None
        
        # Create data directory if it doesn't exist
        if data_dir != ':memory:' and not os.path.exists(data_dir):
            os.makedirs(data_dir, exist_ok=True)
        
        self._initialize_database()
    
    def _initialize_database(self):
        """Initialize database connection and create tables"""
        self.connection = sqlite3.connect(self.db_path, check_same_thread=False)
        self.connection.row_factory = sqlite3.Row  # Enable dict-like access
        self.connection.execute('PRAGMA foreign_keys = ON')  # Enable foreign keys
        
        self._create_tables()
        self._create_genesis_block()
    
    def _create_tables(self):
        """Create all required tables"""
        cursor = self.connection.cursor()
        
        # Transactions table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS transactions (
                id TEXT PRIMARY KEY,
                owner TEXT,
                tags TEXT,  -- JSON string
                target TEXT,
                quantity TEXT,
                reward TEXT,
                signature TEXT,
                last_tx TEXT,
                data_size INTEGER DEFAULT 0,
                content_type TEXT,
                format INTEGER DEFAULT 2,
                height INTEGER DEFAULT 0,
                owner_address TEXT,
                data_root TEXT,
                parent TEXT,
                block TEXT DEFAULT '',
                bundledIn TEXT DEFAULT '',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Blocks table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS blocks (
                id TEXT PRIMARY KEY,
                height INTEGER NOT NULL,
                mined_at INTEGER NOT NULL,  -- Unix timestamp in milliseconds
                previous_block TEXT NOT NULL,
                txs TEXT NOT NULL,  -- JSON array of transaction IDs
                extended TEXT,  -- JSON string for extended data
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Wallets table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS wallets (
                id TEXT PRIMARY KEY,
                address TEXT UNIQUE NOT NULL,
                balance REAL DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Chunks table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS chunks (
                id TEXT PRIMARY KEY,
                chunk TEXT NOT NULL,
                data_root TEXT NOT NULL,
                data_size INTEGER NOT NULL,
                offset INTEGER NOT NULL,
                data_path TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Tags table (separate for better querying)
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS tags (
                tx_id TEXT NOT NULL,
                index_num INTEGER NOT NULL,
                name TEXT,
                value TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (tx_id, index_num),
                FOREIGN KEY (tx_id) REFERENCES transactions(id) ON DELETE CASCADE
            )
        ''')
        
        # Create indexes for better performance
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_transactions_height ON transactions(height)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_transactions_owner_address ON transactions(owner_address)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_transactions_target ON transactions(target)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_transactions_block ON transactions(block)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_blocks_height ON blocks(height)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_wallets_address ON wallets(address)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_tags_name ON tags(name)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_tags_name_value ON tags(name, value)')
        
        self.connection.commit()

    def _create_genesis_block(self):
        """Create genesis block if it doesn't exist"""
        cursor = self.connection.cursor()
        cursor.execute('SELECT COUNT(*) FROM blocks WHERE height = 0')

        if cursor.fetchone()[0] == 0:
            import random
            import string

            def random_id(length: int = 43) -> str:
                chars = string.ascii_letters + string.digits + '-_'
                return ''.join(random.choice(chars) for _ in range(length))

            genesis_id = random_id(64)
            
            cursor.execute('''
                INSERT INTO blocks (id, height, mined_at, previous_block, txs, extended)
                VALUES (?, 0, ?, '', '[]', '{}')
            ''', (genesis_id, int(time.time() * 1000)))
            
            self.connection.commit()
            logger.info(f"Created genesis block: {genesis_id}")
    
    @contextmanager
    def get_cursor(self):
        """Context manager for database cursor"""
        cursor = self.connection.cursor()
        try:
            yield cursor
        finally:
            cursor.close()
    
    def close(self):
        """Close database connection"""
        if self.connection:
            self.connection.close()
    
    # Network state methods
    def get_network_info(self) -> Dict[str, Any]:
        """Get current network information"""
        with self.get_cursor() as cursor:
            # Get latest block
            cursor.execute('SELECT * FROM blocks ORDER BY height DESC LIMIT 1')
            latest_block = cursor.fetchone()
            
            # Get total blocks count
            cursor.execute('SELECT COUNT(*) FROM blocks')
            total_blocks = cursor.fetchone()[0]
            
            # Get pending transactions count
            cursor.execute('SELECT COUNT(*) FROM transactions WHERE block = ""')
            queue_length = cursor.fetchone()[0]
            
            return {
                'height': latest_block['height'] if latest_block else 0,
                'current': latest_block['id'] if latest_block else '',
                'blocks': total_blocks,
                'queue_length': queue_length
            }
    
    def reset_network(self, network_config: Dict[str, Any]) -> str:
        """Reset the entire network state"""
        with self.get_cursor() as cursor:
            # Clear all tables
            cursor.execute('DELETE FROM tags')
            cursor.execute('DELETE FROM transactions')
            cursor.execute('DELETE FROM blocks')
            cursor.execute('DELETE FROM chunks')
            # Keep wallets for convenience
            
            self.connection.commit()
        
        # Recreate genesis block
        self._create_genesis_block()
        
        # Clear data files
        if self.data_dir != ':memory:' and os.path.exists(self.data_dir):
            for filename in os.listdir(self.data_dir):
                if filename.startswith('data-'):
                    os.remove(os.path.join(self.data_dir, filename))
        
        return "Network reset completed"

    # Transaction methods
    def get_transaction(self, txid: str) -> Optional[Dict[str, Any]]:
        """Get transaction by ID"""
        with self.get_cursor() as cursor:
            cursor.execute('SELECT * FROM transactions WHERE id = ?', (txid,))
            tx_row = cursor.fetchone()

            if not tx_row:
                return None

            return self.format_transaction_for_response(tx_row)

    def get_pending_transactions(self) -> List[str]:
        """Get list of pending transaction IDs"""
        with self.get_cursor() as cursor:
            cursor.execute('SELECT id FROM transactions WHERE block = ""')
            return [row[0] for row in cursor.fetchall()]

    def insert_transaction(self, tx_data: Dict[str, Any]) -> bool:
        """Insert a new transaction"""
        try:
            with self.get_cursor() as cursor:
                def tags_to_json(tags):
                    return json.dumps(tags) if tags else '[]'

                # Prepare transaction data
                tx_values = {
                    'id': tx_data['id'],
                    'owner': tx_data.get('owner', ''),
                    'tags': tags_to_json(tx_data.get('tags', [])),
                    'target': tx_data.get('target', ''),
                    'quantity': tx_data.get('quantity', '0'),
                    'reward': tx_data.get('reward', '0'),
                    'signature': tx_data.get('signature', ''),
                    'last_tx': tx_data.get('last_tx', ''),
                    'data_size': int(tx_data.get('data_size', 0)),
                    'content_type': tx_data.get('content_type', ''),
                    'format': int(tx_data.get('format', 2)),
                    'height': int(tx_data.get('height', 0)),
                    'owner_address': tx_data.get('owner_address', tx_data.get('owner', '')),
                    'data_root': tx_data.get('data_root', ''),
                    'parent': tx_data.get('parent', ''),
                    'block': tx_data.get('block', ''),
                    'bundledIn': tx_data.get('bundledIn', '')
                }

                # Insert transaction
                cursor.execute('''
                    INSERT INTO transactions (
                        id, owner, tags, target, quantity, reward, signature, last_tx,
                        data_size, content_type, format, height, owner_address, data_root,
                        parent, block, bundledIn
                    ) VALUES (
                        :id, :owner, :tags, :target, :quantity, :reward, :signature, :last_tx,
                        :data_size, :content_type, :format, :height, :owner_address, :data_root,
                        :parent, :block, :bundledIn
                    )
                ''', tx_values)

                # Insert tags separately
                tags = tx_data.get('tags', [])
                for index, tag in enumerate(tags):
                    cursor.execute('''
                        INSERT INTO tags (tx_id, index_num, name, value)
                        VALUES (?, ?, ?, ?)
                    ''', (tx_data['id'], index, tag.get('name', ''), tag.get('value', '')))

                self.connection.commit()
                return True

        except Exception as e:
            logger.error(f"Error inserting transaction: {e}")
            self.connection.rollback()
            return False

    def delete_transaction(self, txid: str) -> bool:
        """Delete transaction by ID"""
        try:
            with self.get_cursor() as cursor:
                cursor.execute('DELETE FROM transactions WHERE id = ?', (txid,))
                cursor.execute('DELETE FROM tags WHERE tx_id = ?', (txid,))
                self.connection.commit()

                # Delete associated data file
                self._delete_transaction_data(txid)

                return True
        except Exception as e:
            logger.error(f"Error deleting transaction: {e}")
            return False

    def update_transaction_block(self, txid: str, block_id: str, block_height: int) -> bool:
        """Update transaction with block information"""
        try:
            with self.get_cursor() as cursor:
                cursor.execute('''
                    UPDATE transactions
                    SET block = ?, height = ?
                    WHERE id = ?
                ''', (block_id, block_height, txid))
                self.connection.commit()
                return True
        except Exception as e:
            logger.error(f"Error updating transaction block: {e}")
            return False

    # Block methods
    def get_block_by_hash(self, block_hash: str) -> Optional[Dict[str, Any]]:
        """Get block by independent hash"""
        with self.get_cursor() as cursor:
            cursor.execute('SELECT * FROM blocks WHERE id = ?', (block_hash,))
            block_row = cursor.fetchone()

            if not block_row:
                return None

            return self.format_block_for_response(block_row)

    def get_block_by_height(self, height: int) -> Optional[Dict[str, Any]]:
        """Get block by height"""
        with self.get_cursor() as cursor:
            cursor.execute('SELECT * FROM blocks WHERE height = ?', (height,))
            block_row = cursor.fetchone()

            if not block_row:
                return None

            return self.format_block_for_response(block_row)

    def get_latest_block(self) -> Optional[Dict[str, Any]]:
        """Get the latest block"""
        with self.get_cursor() as cursor:
            cursor.execute('SELECT * FROM blocks ORDER BY height DESC LIMIT 1')
            block_row = cursor.fetchone()

            if not block_row:
                return None

            return self.format_block_for_response(block_row)

    def insert_block(self, block_data: Dict[str, Any]) -> bool:
        """Insert a new block"""
        try:
            with self.get_cursor() as cursor:
                cursor.execute('''
                    INSERT INTO blocks (id, height, mined_at, previous_block, txs, extended)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    block_data['id'],
                    block_data['height'],
                    block_data['mined_at'],
                    block_data['previous_block'],
                    json.dumps(block_data.get('txs', [])),
                    json.dumps(block_data.get('extended', {}))
                ))
                self.connection.commit()
                return True
        except Exception as e:
            logger.error(f"Error inserting block: {e}")
            return False

    def mine_blocks(self, qty: int = 1) -> Tuple[bool, List[str]]:
        """Mine blocks and return success status and new block IDs"""
        try:
            import random
            import string

            def random_id(length: int = 43) -> str:
                chars = string.ascii_letters + string.digits + '-_'
                return ''.join(random.choice(chars) for _ in range(length))

            new_block_ids = []

            # Get pending transactions
            pending_txs = self.get_pending_transactions()

            # Get current network state
            network_info = self.get_network_info()
            current_height = network_info['height']
            current_block = network_info['current']

            with self.get_cursor() as cursor:
                for i in range(qty):
                    # Determine which transactions to include
                    block_txs = []
                    if i == qty - 1:  # Add transactions to the last block
                        block_txs = pending_txs

                    # Create new block
                    new_block_id = random_id(64)
                    new_height = current_height + i + 1

                    block_data = {
                        'id': new_block_id,
                        'height': new_height,
                        'mined_at': int(time.time() * 1000),
                        'previous_block': current_block if i == 0 else new_block_ids[-1],
                        'txs': block_txs,
                        'extended': {}
                    }

                    # Insert block
                    cursor.execute('''
                        INSERT INTO blocks (id, height, mined_at, previous_block, txs, extended)
                        VALUES (?, ?, ?, ?, ?, ?)
                    ''', (
                        block_data['id'],
                        block_data['height'],
                        block_data['mined_at'],
                        block_data['previous_block'],
                        json.dumps(block_data['txs']),
                        json.dumps(block_data['extended'])
                    ))

                    new_block_ids.append(new_block_id)
                    current_block = new_block_id

                # Update transactions with block information
                if pending_txs and new_block_ids:
                    final_block_id = new_block_ids[-1]
                    final_height = current_height + qty

                    for txid in pending_txs:
                        cursor.execute('''
                            UPDATE transactions
                            SET block = ?, height = ?
                            WHERE id = ?
                        ''', (final_block_id, final_height, txid))

                self.connection.commit()
                return True, new_block_ids

        except Exception as e:
            logger.error(f"Error mining blocks: {e}")
            self.connection.rollback()
            return False, []

    # Wallet methods
    def get_wallet(self, address: str) -> Optional[Dict[str, Any]]:
        """Get wallet by address"""
        with self.get_cursor() as cursor:
            cursor.execute('SELECT * FROM wallets WHERE address = ?', (address,))
            wallet_row = cursor.fetchone()

            if not wallet_row:
                return None

            return dict(wallet_row)

    def get_wallet_balance(self, address: str) -> float:
        """Get wallet balance"""
        wallet = self.get_wallet(address)
        return wallet['balance'] if wallet else 0.0

    def create_wallet(self, address: str, balance: float = 0.0) -> bool:
        """Create a new wallet"""
        try:
            import random
            import string

            def random_id(length: int = 43) -> str:
                chars = string.ascii_letters + string.digits + '-_'
                return ''.join(random.choice(chars) for _ in range(length))

            with self.get_cursor() as cursor:
                cursor.execute('''
                    INSERT INTO wallets (id, address, balance)
                    VALUES (?, ?, ?)
                ''', (random_id(64), address, balance))
                self.connection.commit()
                return True
        except Exception as e:
            logger.error(f"Error creating wallet: {e}")
            return False

    def update_wallet_balance(self, address: str, balance: float) -> bool:
        """Update wallet balance"""
        try:
            with self.get_cursor() as cursor:
                # Check if wallet exists
                cursor.execute('SELECT id FROM wallets WHERE address = ?', (address,))
                if not cursor.fetchone():
                    # Create wallet if it doesn't exist
                    self.create_wallet(address, balance)
                else:
                    # Update existing wallet
                    cursor.execute('''
                        UPDATE wallets SET balance = ? WHERE address = ?
                    ''', (balance, address))

                self.connection.commit()
                return True
        except Exception as e:
            logger.error(f"Error updating wallet balance: {e}")
            return False

    def increment_wallet_balance(self, address: str, amount: float) -> float:
        """Add amount to wallet balance and return new balance"""
        try:
            current_balance = self.get_wallet_balance(address)
            new_balance = current_balance + amount

            if self.update_wallet_balance(address, new_balance):
                return new_balance
            return current_balance
        except Exception as e:
            logger.error(f"Error incrementing wallet balance: {e}")
            return self.get_wallet_balance(address)

    def get_wallet_last_transaction(self, address: str) -> Optional[str]:
        """Get the last transaction ID for a wallet"""
        with self.get_cursor() as cursor:
            cursor.execute('''
                SELECT id FROM transactions
                WHERE owner_address = ? OR target = ?
                ORDER BY created_at DESC
                LIMIT 1
            ''', (address, address))

            result = cursor.fetchone()
            return result[0] if result else None

    # Chunk methods
    def insert_chunk(self, chunk_data: Dict[str, Any]) -> bool:
        """Insert a new chunk"""
        try:
            import random
            import string

            def random_id(length: int = 43) -> str:
                chars = string.ascii_letters + string.digits + '-_'
                return ''.join(random.choice(chars) for _ in range(length))

            with self.get_cursor() as cursor:
                cursor.execute('''
                    INSERT INTO chunks (id, chunk, data_root, data_size, offset, data_path)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    random_id(64),
                    chunk_data['chunk'],
                    chunk_data.get('data_root', ''),
                    int(chunk_data.get('data_size', 0)),
                    int(chunk_data.get('offset', 0)),
                    chunk_data.get('data_path', '')
                ))
                self.connection.commit()
                return True
        except Exception as e:
            logger.error(f"Error inserting chunk: {e}")
            return False

    def get_chunk_by_offset(self, offset: int) -> Optional[Dict[str, Any]]:
        """Get chunk by offset"""
        with self.get_cursor() as cursor:
            cursor.execute('SELECT * FROM chunks WHERE offset = ?', (offset,))
            chunk_row = cursor.fetchone()

            if not chunk_row:
                return None

            return dict(chunk_row)

    def get_next_chunk_offset(self) -> int:
        """Calculate the next chunk offset"""
        with self.get_cursor() as cursor:
            cursor.execute('SELECT MAX(offset) FROM chunks')
            result = cursor.fetchone()
            max_offset = result[0] if result and result[0] is not None else 0

            if max_offset == 0:
                return 1000  # Starting offset

            # Get the chunk at max offset to calculate next offset
            chunk = self.get_chunk_by_offset(max_offset)
            if chunk:
                return max_offset + len(chunk['chunk'])

            return max_offset + 1000  # Default increment

    def get_chunks_by_data_root(self, data_root: str) -> List[Dict[str, Any]]:
        """Get all chunks for a given data_root"""
        chunks = []
        with self.get_cursor() as cursor:
            cursor.execute('SELECT * FROM chunks WHERE data_root = ? ORDER BY offset', (data_root,))
            results = cursor.fetchall()
            for result in results:
                chunks.append(dict(result))
        return chunks

    def _delete_transaction_data(self, txid: str) -> bool:
        """Delete transaction data from disk"""
        try:
            if self.data_dir == ':memory:':
                return True  # Skip file operations for in-memory mode

            filepath = os.path.join(self.data_dir, f'data-{txid}')

            if os.path.exists(filepath):
                os.remove(filepath)

            return True
        except Exception as e:
            logger.error(f"Error deleting transaction data: {e}")
            return False

    def format_transaction_for_response(self, tx_row: Any) -> Dict[str, Any]:
        """Format transaction row for API response"""
        if not tx_row:
            return {}

        # Convert sqlite3.Row to dict
        tx = dict(tx_row)

        # Parse JSON fields
        if tx.get('tags'):
            try:
                tx['tags'] = json.loads(tx['tags'])
            except (json.JSONDecodeError, TypeError):
                tx['tags'] = []
        else:
            tx['tags'] = []

        # Ensure data_size is string for compatibility
        if tx.get('data_size') is not None:
            tx['data_size'] = str(tx['data_size'])

        return tx

    def format_block_for_response(self, block_row: Any) -> Dict[str, Any]:
        """Format block row for API response"""
        if not block_row:
            return {}

        # Convert sqlite3.Row to dict
        block = dict(block_row)

        # Parse JSON fields
        if block.get('txs'):
            try:
                block['txs'] = json.loads(block['txs'])
            except (json.JSONDecodeError, TypeError):
                block['txs'] = []
        else:
            block['txs'] = []

        if block.get('extended'):
            try:
                block['extended'] = json.loads(block['extended'])
            except (json.JSONDecodeError, TypeError):
                block['extended'] = {}

        # Convert mined_at to seconds for API compatibility
        if block.get('mined_at'):
            block['timestamp'] = block['mined_at'] // 1000

        return block
